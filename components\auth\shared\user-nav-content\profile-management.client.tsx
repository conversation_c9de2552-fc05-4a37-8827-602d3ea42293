// profile-management.tsx
'use client';
import { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { useUnifiedSession } from '../../../../lib/auth-client';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { AvatarUpload } from '../../../ui/avatar-upload.client';
import { getImageUrl } from '../../../../lib/utils/image';
import { ThemeBackgroundDialog } from './theme-background-dialog.client';
import { useTheme } from '../../../providers/theme-provider.client';
import {UserIcon, RoleIcon, ColorThemeIcon} from "../../../ui/icons"
import {
  AtSign,
  Edit3,
  Settings
} from 'lucide-react';
import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../../../ui/buttons/input';

interface ProfileManagementProps {
  className?: string;
}

export function ProfileManagement({ className }: ProfileManagementProps) {
  const { session } = useUnifiedSession();
  const user = session?.user;
  const userInitials = useUserInitials(user?.name);
  const { theme, backgroundTheme } = useTheme();

  // Process user image data
  const userImageUrl = getImageUrl(user?.image);
  
  const [editedName, setEditedName] = useState(user?.name || '');
  const [isSaving, setIsSaving] = useState(false);
  const [showNameInput, setShowNameInput] = useState(false);
  const [nameMessage, setNameMessage] = useState('');
  const [nameMessageType, setNameMessageType] = useState<'success' | 'error' | ''>('');

  // Theme dialog state
  const [isThemeDialogOpen, setIsThemeDialogOpen] = useState(false);

  // Sync editedName with user name when user changes
  useEffect(() => {
    setEditedName(user?.name || '');
  }, [user?.name]);

  // Handle avatar update
  const handleAvatarUpdate = useCallback((imageUrl: string | null) => {
    console.log('Avatar updated:', imageUrl);
    // The avatar update hook will handle the actual database update
    // and refresh the session, so the UI will update automatically
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!user || editedName.trim() === user.name) {
      setShowNameInput(false);
      setNameMessage('');
      setNameMessageType('');
      return;
    }

    setIsSaving(true);
    setNameMessage('');
    setNameMessageType('');

    try {
      // TODO: Implement name update mutation
      console.log('Saving name update:', editedName.trim());

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success message
      setNameMessage('Name updated successfully!');
      setNameMessageType('success');

      // Auto-hide success message and close input after 3 seconds
      setTimeout(() => {
        setNameMessage('');
        setNameMessageType('');
        setShowNameInput(false);
      }, 3000);

    } catch (error) {
      console.error('Failed to update name:', error);
      setNameMessage('Failed to update name. Please try again.');
      setNameMessageType('error');
    } finally {
      setIsSaving(false);
    }
  }, [user, editedName]);

  // Handle name form submission
  const handleNameSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (editedName.trim() && editedName.trim() !== user?.name) {
      handleSaveEdit();
    }
  }, [editedName, user?.name, handleSaveEdit]);

  // Get theme display name
  const getThemeDisplayName = useCallback((themeValue: string) => {
    switch (themeValue) {
      case 'light': return 'Light Mode';
      case 'dark': return 'Dark Mode';
      case 'system': return 'System (Auto)';
      default: return 'System (Auto)';
    }
  }, []);

  // Get background display name
  const getBackgroundDisplayName = useCallback((backgroundId: string) => {
    const backgroundNames: Record<string, string> = {
      'default': 'Default',
      'horizon-glow-bottom': 'Horizon Glow (Bottom)',
      'crimson-depth-bottom': 'Crimson Depth (Bottom)',
      'emerald-void-bottom': 'Emerald Void (Bottom)',
      'violet-abyss-bottom': 'Violet Abyss (Bottom)',
      'azure-depths-bottom': 'Azure Depths (Bottom)',
      'orchid-depths-bottom': 'Orchid Depths (Bottom)',
      'horizon-glow-top': 'Horizon Glow (Top)',
      'crimson-depth-top': 'Crimson Depth (Top)',
      'emerald-void-top': 'Emerald Void (Top)',
      'violet-abyss-top': 'Violet Abyss (Top)',
      'azure-depths-top': 'Azure Depths (Top)',
      'orchid-depths-top': 'Orchid Depths (Top)'
    };
    return backgroundNames[backgroundId] || 'Default';
  }, []);

  if (!user) {
    return (
      <div className="p-4 h-full flex items-center justify-center">
        <p className="text-white/70">Loading profile...</p>
      </div>
    );
  }

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'student': return 'Student';
      case 'teacher': return 'Teacher';
      case 'school_admin': return 'School Administrator';
      default: return role;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'student': return 'text-blue-400';
      case 'teacher': return 'text-green-400';
      case 'school_admin': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <>
      <div className={cn("mb-20", className)}>
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          
          {/* Left Column - Profile Information */}
          <div className="space-y-6 rounded-xl bg-white/85 dark:bg-zinc-900/85 backdrop-blur-md border border-white/5 dark:border-zinc-700/5 transition-all duration-200 overflow-hidden p-4">
            
            {/* Avatar Section */}
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
              <div className="flex justify-between items-center">
                <div className="text-left">
                  <p className="text-sm font-manrope_1 font-semibold text-black/90 dark:text-white/90 mb-1">Profile Picture</p>
                  <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50">Click or drag to upload a new avatar</p>
                </div>
                <AvatarUpload
                  currentImage={user.image}
                  userInitials={userInitials}
                  userName={user.name || user.email}
                  onImageUpdate={handleAvatarUpdate}
                  size="xs"
                  className=""
                />
                
              </div>
            </div>


{/* Name Field */}
<div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
  <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
    <div className="flex items-center gap-2 md:min-w-0 md:flex-shrink-0">
      <UserIcon className="h-3 w-3 text-black/70 dark:text-white/70" />
      <span className="text-sm font-manrope_1 font-semibold text-black/90 dark:text-white/90">Full Name</span>
    </div>

    <form onSubmit={handleNameSubmit} className="w-full md:flex-1">
      <InputButtonProvider
        showInput={showNameInput}
        setShowInput={setShowNameInput}
        transition={{ type: 'spring', stiffness: 400, damping: 30 }}
        className="!w-full relative group items-center justify-center h-10 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
      >
        <InputButton onClick={(e) => {
          // Prevent form submission when clicking the input area
          if (showNameInput) {
            e.preventDefault();
            e.stopPropagation();
          }
        }}>
          <AnimatePresence>
            {!showNameInput && (
              <motion.div
                key="name-display"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="w-full"
              >
                <InputButtonAction className="flex items-center justify-between w-full px-2">
                  <span className="text-black/80 dark:text-white/80 font-manrope_1 text-sm">
                    {user?.name || 'Click to set name'}
                  </span>
                  <Edit3 className="h-3 w-3 text-black/70 dark:text-white/70" />
                </InputButtonAction>
              </motion.div>
            )}
          </AnimatePresence>

          <InputButtonSubmit
            type="submit"
            disabled={isSaving || !editedName.trim() || editedName.trim() === user?.name}
            message={nameMessage}
            messageType={nameMessageType}
            isSubmitting={isSaving}
            onClick={(e) => {
              // Only submit if we have changes, otherwise prevent default behavior
              if (showNameInput && editedName.trim() && editedName.trim() !== user?.name) {
                // Let the form submit naturally
                return;
              }
              // Prevent the default toggle behavior from InputButtonSubmit
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            {isSaving ? 'Saving...' : 'Save Name'}
          </InputButtonSubmit>

          {showNameInput && (
            <div onClick={(e) => e.stopPropagation()}>
              <InputButtonInput
                type="text"
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                placeholder="Enter your full name"
                disabled={isSaving}
                autoFocus
              />
            </div>
          )}
        </InputButton>
      </InputButtonProvider>
    </form>
  </div>
</div>
            {/* Username Field (for students) */}
            {user.role === 'student' && (user as any).username && (
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
                <div className="flex items-center gap-2 mb-2">
                  <AtSign className="h-3 w-3 text-black/70 dark:text-white/70" />
                  <span className="text-sm font-manrope_1 font-semibold text-black/90 dark:text-white/90">Username</span>
                </div>
                <p className="text-xs rounded-full font-manrope_1 text-black/80 dark:text-white/80 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] ">{(user as any).username}</p>
                <p className="text-xs rounded-full font-manrope_1 text-black/50 dark:text-white/50 mt-1 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">Username assigned by your teacher</p>
              </div>
            )}

          </div>

          {/* Right Column - Settings & Status */}
          <div className="space-y-6 rounded-xl bg-white/85 dark:bg-zinc-900/85 backdrop-blur-md border border-white/5 dark:border-zinc-700/5 transition-all duration-200 overflow-hidden p-4">
            
            {/* Role Field */}
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RoleIcon className="h-3 w-3 text-black/70 dark:text-white/70" />
                <span className="text-sm font-manrope_1 font-semibold text-black/90 dark:text-white/90">Role</span>
              </div>
              <p className={cn("font-manrope_1 text-xs text-black/60 dark:text-white/60 p-2 rounded-full shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]", getRoleColor(user.role))}>
                {getRoleDisplayName(user.role)}
              </p>
            </div>
            
            </div>

            {/* Theme & Background Settings */}
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <ColorThemeIcon className="h-4 w-4 text-black/70 dark:text-white/70" />
                  <span className="text-sm font-manrope_1 font-semibold text-black/90 dark:text-white/90">Theme & Background</span>
                </div>
                <button
                  onClick={() => setIsThemeDialogOpen(true)}
                  className="p-1 hover:bg-black/10 dark:hover:bg-white/10 rounded transition-colors"
                >
                  <Settings className="h-3 w-3 text-black/70 dark:text-white/70" />
                </button>
              </div>
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-manrope_1 font-semibold text-black/80 dark:text-white/80 font-manrope_1">Theme Mode</span>
                  <span className="text-xs dont-manrope_1 text-black/60 dark:text-white/60 font-manrope_1">{getThemeDisplayName(theme)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs font-manrope_1 font-semibold text-black/80 dark:text-white/80 font-manrope_1">Background</span>
                  <span className="text-xs font-manrope_1  text-black/60 dark:text-white/60 font-manrope_1">{getBackgroundDisplayName(backgroundTheme)}</span>
                </div>
              </div>
              <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50 mt-2">Click settings to customize your theme</p>
            </div>

            {/* Account Status */}
            <div className="space-y-2 rounded-md p-2 bg-zinc-200/60 dark:bg-zinc-800/60">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-manrope_1 font-semibold text-black/80 dark:text-white/80 mb-1">Account Status</p>
                 
                </div>
                <div className="flex items-center gap-2 p-2 rounded-full shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs font-manrope_1 text-green-400">Active</span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Theme Background Dialog */}
      <ThemeBackgroundDialog
        isOpen={isThemeDialogOpen}
        onOpenChange={setIsThemeDialogOpen}
      />
    </>
  );
}